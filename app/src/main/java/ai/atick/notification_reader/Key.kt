package ai.atick.notification_reader

internal object Key {
    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    const val ID: Int = 9090

    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    const val TAG: String = "FirebaseJobService"

    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    const val INTERVAL: Long = 60000 //900000; //in ms (60 * 1000 -> 1 minute)

    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    @kotlin.jvm.JvmField
    var jobCompleted: Boolean =
        false /**///////////////////////////////////////////////////////////////////////////////////////////// */
}
