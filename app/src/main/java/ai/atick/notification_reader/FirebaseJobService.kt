package ai.atick.notification_reader

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.job.JobParameters
import android.app.job.JobService
import android.content.Context
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import java.text.SimpleDateFormat
import java.util.*

class FirebaseJobService : JobService() {
    ////////////////////////////////////////////////////////////////
    private val database = FirebaseDatabase.getInstance()
    private val reference = database.reference
    private lateinit var appDatabase: AppDatabase
    private val uploadList = ArrayList<String>()

    companion object {
        private const val CHANNEL_ID = "firebase_job_channel"
        private const val NOTIFICATION_ID_START = 1001
        private const val NOTIFICATION_ID_STOP = 1002
    }

    ////////////////////////////////////////////////////////////////
    override fun onStartJob(params: JobParameters): Boolean {
        Log.d(Key.TAG, "Uploading to Firebase")
        createNotificationChannel()
        showNotification("Firebase Upload Started", "Uploading notifications to Firebase...", NOTIFICATION_ID_START)
        doInBackground()
        return false
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    override fun onStopJob(params: JobParameters): Boolean {
        Log.d(Key.TAG, "Upload cancelled")
        showNotification("Firebase Upload Cancelled", "Upload process was cancelled", NOTIFICATION_ID_STOP)
        return true
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    private fun doInBackground() {
        appDatabase = AppDatabase(applicationContext)
        uploadList.clear()
        uploadList.addAll(appDatabase.getListString("Notification_List"))
        val sdf = SimpleDateFormat("yyyy|MM|dd-HH:mm:ss", Locale.getDefault())
        val currentDateAndTime = sdf.format(Date())
        reference.child(currentDateAndTime).setValue(uploadList) { databaseError: DatabaseError?, _: DatabaseReference ->
            Key.jobCompleted = true
            Log.d(Key.TAG, "Firebase upload finished")
            showNotification("Firebase Upload Complete", "Successfully uploaded ${uploadList.size} notifications", NOTIFICATION_ID_STOP)
            uploadList.clear()
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Firebase Job Service"
            val descriptionText = "Notifications for Firebase upload job status"
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    private fun showNotification(title: String, content: String, notificationId: Int) {
        try {
            val builder = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle(title)
                .setContentText(content)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true)

            with(NotificationManagerCompat.from(this)) {
                notify(notificationId, builder.build())
            }
        } catch (e: SecurityException) {
            Log.e(Key.TAG, "Permission denied for showing notification: ${e.message}")
        } catch (e: Exception) {
            Log.e(Key.TAG, "Error showing notification: ${e.message}")
        }
    }
}
