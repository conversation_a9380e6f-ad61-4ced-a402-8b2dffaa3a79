package ai.atick.notification_reader

import android.app.job.JobParameters
import android.app.job.JobService
import android.util.Log
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import java.text.SimpleDateFormat
import java.util.*

class FirebaseJobService : JobService() {
    ////////////////////////////////////////////////////////////////
    private val database = FirebaseDatabase.getInstance()
    private val reference = database.reference
    private lateinit var appDatabase: AppDatabase
    private val uploadList = ArrayList<String>()

    ////////////////////////////////////////////////////////////////
    override fun onStartJob(params: JobParameters): Boolean {
        Log.d(Key.TAG, "Uploading to Firebase")
        doInBackground()
        return false
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    override fun onStopJob(params: JobParameters): Boolean {
        Log.d(Key.TAG, "Upload cancelled")
        return true
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    private fun doInBackground() {
        appDatabase = AppDatabase(applicationContext)
        uploadList.clear()
        uploadList.addAll(appDatabase.getListString("Notification_List"))
        val sdf = SimpleDateFormat("yyyy|MM|dd-HH:mm:ss", Locale.getDefault())
        val currentDateAndTime = sdf.format(Date())
        reference.child(currentDateAndTime).setValue(uploadList) { databaseError: DatabaseError?, _: DatabaseReference ->
            Key.jobCompleted = true
            Log.d(Key.TAG, "Firebase upload finished")
            uploadList.clear()
        }
    }
}
