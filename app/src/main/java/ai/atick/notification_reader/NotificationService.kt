package ai.atick.notification_reader

import ai.atick.notification_reader.Key.TAG
import android.os.Bundle
import com.google.firebase.database.logging.Logger
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification

class NotificationService : NotificationListenerService() {
    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    var notificationList: ArrayList<String?> = ArrayList()
    var appDatabase: AppDatabase? = null
    var _packageName: String? = "no-name"
    var title: String? = "Untitled"
    var text: String? = "empty"

    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    override fun onCreate() {
        super.onCreate()
        appDatabase = AppDatabase(getApplicationContext())
    }

    /**///////////////////////////////////////////////////////////////////////////////////////////// */
    override fun onNotificationPosted(sbn: StatusBarNotification) { /**////////////////////////////////////////////////////////// */
        if (Key.jobCompleted) {
            notificationList.clear()
            appDatabase!!.remove("Notification_List")
            Key.jobCompleted = false
        }
        println( "onNotificationPosted")
        println( "Package Name : " + sbn.packageName)

        //        println( "Title : " + sbn.getNotification().extras.getString("android.title"));
//        println( "Text : " + sbn.getNotification().extras.getString("android.text"));
        /**////////////////////////////////////////////////////////// */
        _packageName= sbn.packageName
        if (_packageName.equals("com.facebook.orca")) {
            val extras: Bundle? = sbn.getNotification().extras
            if (extras != null) {
                title = extras.getString("android.title")
                if (title != null && !title.equals("Chat heads active")) {
                    text = extras.getString("android.text")
                    val notification = title.toString() + " : " + text
                    notificationList.add(notification)
                    println( notification)
                }
            }
        } else {
            val extras: Bundle? = sbn.getNotification().extras
            if (extras != null) {
                title = extras.getString("android.title")
                text = extras.getString("android.text")
                val notification = title.toString() + " : " + text
                notificationList.add(notification)
                print("notifications: ")
                println( notification)
            }
        }
        appDatabase!!.putListString("Notification_List", notificationList)
    }
}
