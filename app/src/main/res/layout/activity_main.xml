<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <LinearLayout
        android:id="@+id/settings_prompt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="24dp"
        android:background="#FFFFFF"
        android:elevation="8dp"
        android:orientation="vertical"
        android:padding="8dp"
        tools:ignore="UselessParent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:text="@string/please_turn_on_notification_access"
            android:textAlignment="center"
            android:textSize="20sp" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:contentDescription="@string/settings_prompt" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:text="@string/go_to_settings"
            android:textAlignment="viewEnd"
            android:textColor="@color/colorPrimary"
            android:textSize="20sp" />

    </LinearLayout>

</FrameLayout>