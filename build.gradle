// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    
    ext {
        kotlin_version = '2.0.21'
    }
    repositories {
        google()
        jcenter()
        
    }
    dependencies {
        // This version might need to be updated
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.7.20"

//        classpath 'com.android.tools.build:gradle:3.6.0'
        classpath 'com.google.gms:google-services:4.4.3'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"


        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
